{"rustc": 12610991425282158916, "features": "[\"perf-literal\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"perf-literal\", \"std\"]", "target": 7534583537114156500, "profile": 16503403049695105087, "path": 247052673307217138, "deps": [[15932120279885307830, "memchr", false, 6733121983210285479]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/aho-corasick-4359c345230efac0/dep-lib-aho_corasick", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}