{"rustc": 12610991425282158916, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 16503403049695105087, "path": 15406276965172582966, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/memchr-00cf34f32dd14e68/dep-lib-memchr", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}