{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5157631553186200874, "build_script_build", false, 480210458193903058]], "local": [{"RerunIfChanged": {"output": "release/build/num-traits-1162b9159723cb45/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}