{"rustc": 12610991425282158916, "features": "[\"default\", \"derive\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 16256121404318112599, "profile": 16503403049695105087, "path": 9962637312496767763, "deps": [[9689903380558560274, "build_script_build", false, 10438270610804990070], [16257276029081467297, "serde_derive", false, 4296305665411978897]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/serde-459a52a88b20fed5/dep-lib-serde", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}