{"rustc": 12610991425282158916, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 15879119289217769706, "profile": 16503403049695105087, "path": 13953567547140080106, "deps": [[779169961665879123, "foreign_types_macros", false, 13657508290186636054], [5934578708587553847, "foreign_types_shared", false, 1431918528799326329]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/foreign-types-fbec3af7a18b25f2/dep-lib-foreign_types", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}