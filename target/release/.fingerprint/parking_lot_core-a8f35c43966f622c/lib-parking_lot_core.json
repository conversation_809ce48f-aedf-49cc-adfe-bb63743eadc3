{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 16503403049695105087, "path": 13727943380544612360, "deps": [[2828590642173593838, "cfg_if", false, 3850821628733579518], [3666196340704888985, "smallvec", false, 5596403895100658797], [4269498962362888130, "build_script_build", false, 17064915164975589487], [4684437522915235464, "libc", false, 7103501291586514795]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/parking_lot_core-a8f35c43966f622c/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}