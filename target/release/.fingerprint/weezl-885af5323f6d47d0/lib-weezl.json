{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 16503403049695105087, "path": 14057936491379656041, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/weezl-885af5323f6d47d0/dep-lib-weezl", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}