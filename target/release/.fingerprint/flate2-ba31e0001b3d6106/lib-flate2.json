{"rustc": 12610991425282158916, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 16503403049695105087, "path": 10166535069276550808, "deps": [[7312356825837975969, "crc32fast", false, 7975673435513412229], [7636735136738807108, "miniz_oxide", false, 3373359164647900397]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-ba31e0001b3d6106/dep-lib-flate2", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}