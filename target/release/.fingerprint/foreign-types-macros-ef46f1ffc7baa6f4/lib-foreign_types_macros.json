{"rustc": 12610991425282158916, "features": "[\"std\"]", "declared_features": "[\"std\"]", "target": 11709244145291352760, "profile": 17257705230225558938, "path": 4921347393011404533, "deps": [[3060637413840920116, "proc_macro2", false, 3707371440374171623], [4974441333307933176, "syn", false, 7416688503794151229], [17990358020177143287, "quote", false, 9034593943365996366]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/foreign-types-macros-ef46f1ffc7baa6f4/dep-lib-foreign_types_macros", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}