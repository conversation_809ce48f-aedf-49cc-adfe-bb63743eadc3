{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 274225931431160061, "profile": 16503403049695105087, "path": 6319200759475363500, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/foreign-types-shared-846e8be379ed9948/dep-lib-foreign_types_shared", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}