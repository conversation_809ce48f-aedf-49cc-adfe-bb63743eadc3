{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"arbitrary\", \"bytemuck\", \"default\", \"num-traits\", \"rand_distr\", \"rkyv\", \"serde\", \"std\", \"use-intrinsics\", \"zerocopy\"]", "target": 5584728948347947946, "profile": 16503403049695105087, "path": 12863885254191991013, "deps": [[2828590642173593838, "cfg_if", false, 3850821628733579518]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/half-bb89db9fad645b9e/dep-lib-half", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}