{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 17257705230225558938, "path": 17710424885886743353, "deps": [[1988483478007900009, "unicode_ident", false, 5561105984380045314], [3060637413840920116, "proc_macro2", false, 3707371440374171623], [17990358020177143287, "quote", false, 9034593943365996366]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-dcfc030becd20fa0/dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}