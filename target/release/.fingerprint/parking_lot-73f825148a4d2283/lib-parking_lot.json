{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 9887373948397848517, "profile": 16503403049695105087, "path": 10822223464571473043, "deps": [[4269498962362888130, "parking_lot_core", false, 9759611292063526315], [8081351675046095464, "lock_api", false, 6248201183377925627]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/parking_lot-73f825148a4d2283/dep-lib-parking_lot", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}