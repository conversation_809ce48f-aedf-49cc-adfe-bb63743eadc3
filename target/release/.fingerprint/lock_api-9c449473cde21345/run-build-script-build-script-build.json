{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8081351675046095464, "build_script_build", false, 6731584631925351171]], "local": [{"RerunIfChanged": {"output": "release/build/lock_api-9c449473cde21345/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}