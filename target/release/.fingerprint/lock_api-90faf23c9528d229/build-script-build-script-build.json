{"rustc": 12610991425282158916, "features": "[\"atomic_usize\", \"default\"]", "declared_features": "[\"arc_lock\", \"atomic_usize\", \"default\", \"nightly\", \"owning_ref\", \"serde\"]", "target": 5408242616063297496, "profile": 17257705230225558938, "path": 17219021719097010156, "deps": [[13927012481677012980, "autocfg", false, 15189704415728028409]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/lock_api-90faf23c9528d229/dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}