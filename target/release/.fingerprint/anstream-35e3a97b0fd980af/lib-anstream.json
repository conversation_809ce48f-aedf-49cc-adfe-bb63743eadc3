{"rustc": 12610991425282158916, "features": "[\"auto\", \"default\", \"wincon\"]", "declared_features": "[\"auto\", \"default\", \"test\", \"wincon\"]", "target": 11278316191512382530, "profile": 13797464687679284244, "path": 9460974202620678123, "deps": [[384403243491392785, "colorchoice", false, 10921230470393753277], [6062327512194961595, "is_terminal_polyfill", false, 9086673703401756290], [9394696648929125047, "anstyle", false, 465800501779716895], [11410867133969439143, "anstyle_parse", false, 18389167191179692667], [12500913394773746471, "anstyle_query", false, 7472673183329711020], [17716308468579268865, "utf8parse", false, 11964248083848183404]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/anstream-35e3a97b0fd980af/dep-lib-anstream", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}