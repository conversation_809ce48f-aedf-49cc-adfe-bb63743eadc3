{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 3344701623380386, "path": 3833266431416864318, "deps": [[3060637413840920116, "proc_macro2", false, 3707371440374171623], [4974441333307933176, "syn", false, 7416688503794151229], [13077543566650298139, "heck", false, 2354590992817755451], [17990358020177143287, "quote", false, 9034593943365996366]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_derive-8201f759993ad1f3/dep-lib-clap_derive", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}