{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4684437522915235464, "build_script_build", false, 3555181329941481495]], "local": [{"RerunIfChanged": {"output": "release/build/libc-2211293c1b6255ec/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_MUSL_V1_2_3", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS", "val": null}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_GNU_TIME_BITS", "val": null}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}