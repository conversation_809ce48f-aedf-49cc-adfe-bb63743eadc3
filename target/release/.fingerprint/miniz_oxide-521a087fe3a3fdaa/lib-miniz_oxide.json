{"rustc": 12610991425282158916, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11301701693415961412, "path": 2166183391408411820, "deps": [[4018467389006652250, "simd_adler32", false, 13941282146255776889], [7911289239703230891, "adler2", false, 14808888205183417795]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/miniz_oxide-521a087fe3a3fdaa/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}