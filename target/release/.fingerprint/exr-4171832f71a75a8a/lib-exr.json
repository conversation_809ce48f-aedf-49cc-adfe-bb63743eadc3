{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 7534689274471719961, "profile": 16503403049695105087, "path": 10878111809103473907, "deps": [[3666196340704888985, "smallvec", false, 5596403895100658797], [7636735136738807108, "miniz_oxide", false, 3373359164647900397], [7774066819721693875, "bit_field", false, 12969922357391509961], [9341542296028756253, "lebe", false, 2732313978284535903], [9705675356647965917, "rayon_core", false, 2131828562552319497], [11952083740819019228, "zune_inflate", false, 6189029117053533773], [16857843618210199216, "half", false, 13454120408260945160]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/exr-4171832f71a75a8a/dep-lib-exr", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}