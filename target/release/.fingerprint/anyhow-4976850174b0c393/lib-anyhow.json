{"rustc": 12610991425282158916, "features": "[\"default\", \"std\"]", "declared_features": "[\"backtrace\", \"default\", \"std\"]", "target": 16100955855663461252, "profile": 16503403049695105087, "path": 14311056222020920358, "deps": [[13625485746686963219, "build_script_build", false, 9590634494798700201]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/anyhow-4976850174b0c393/dep-lib-anyhow", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}