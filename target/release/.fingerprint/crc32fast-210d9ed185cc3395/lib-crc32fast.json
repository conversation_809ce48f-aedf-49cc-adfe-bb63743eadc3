{"rustc": 12610991425282158916, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"nightly\", \"std\"]", "target": 10823605331999153028, "profile": 16503403049695105087, "path": 12069880441498519530, "deps": [[2828590642173593838, "cfg_if", false, 3850821628733579518], [7312356825837975969, "build_script_build", false, 4117350288827509934]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/crc32fast-210d9ed185cc3395/dep-lib-crc32fast", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}