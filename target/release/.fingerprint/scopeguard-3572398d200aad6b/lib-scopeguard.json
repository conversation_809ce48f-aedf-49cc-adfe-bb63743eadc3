{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"default\", \"use_std\"]", "target": 3556356971060988614, "profile": 16503403049695105087, "path": 3623025817293775986, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/scopeguard-3572398d200aad6b/dep-lib-scopeguard", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}