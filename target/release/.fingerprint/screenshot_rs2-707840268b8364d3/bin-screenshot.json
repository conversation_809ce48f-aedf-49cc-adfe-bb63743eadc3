{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 1743929974723899041, "profile": 5353520049763563864, "path": 4942398508502643691, "deps": [[26874729151426918, "clap", false, 6503109244844458551], [2141549406637498597, "image", false, 14612470370704510805], [5986029879202738730, "log", false, 12442427843341601211], [8008191657135824715, "thiserror", false, 10910124674402202331], [8256202458064874477, "dirs", false, 12121892020227629240], [9689903380558560274, "serde", false, 6596204602844417774], [9897246384292347999, "chrono", false, 8408944238898689313], [11996286768261087171, "screenshots", false, 5056563757530993260], [12103695930867503580, "env_logger", false, 15132695465347932819], [13625485746686963219, "anyhow", false, 13304049009205258878], [16362055519698394275, "serde_json", false, 2776205312526588648], [17455546449572272057, "arboard", false, 8404861138337185038], [17531218394775549125, "tokio", false, 11135548386489914306]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/screenshot_rs2-707840268b8364d3/dep-bin-screenshot", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}