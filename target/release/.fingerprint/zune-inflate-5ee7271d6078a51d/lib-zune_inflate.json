{"rustc": 12610991425282158916, "features": "[\"simd-adler32\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"simd-adler32\", \"std\", \"zlib\"]", "target": 12020662131698132232, "profile": 16503403049695105087, "path": 11780285439653229911, "deps": [[4018467389006652250, "simd_adler32", false, 13941282146255776889]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zune-inflate-5ee7271d6078a51d/dep-lib-zune_inflate", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}