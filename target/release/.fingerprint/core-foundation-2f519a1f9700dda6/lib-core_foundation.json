{"rustc": 12610991425282158916, "features": "[\"default\", \"link\"]", "declared_features": "[\"chrono\", \"default\", \"link\", \"mac_os_10_7_support\", \"mac_os_10_8_features\", \"uuid\", \"with-chrono\", \"with-uuid\"]", "target": 3908465493571680068, "profile": 16503403049695105087, "path": 976936853042443339, "deps": [[4684437522915235464, "libc", false, 7103501291586514795], [12589608519315293066, "core_foundation_sys", false, 1394163907186765794]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/core-foundation-2f519a1f9700dda6/dep-lib-core_foundation", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}