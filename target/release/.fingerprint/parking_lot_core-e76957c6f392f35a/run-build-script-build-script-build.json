{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4269498962362888130, "build_script_build", false, 11419568340066379831]], "local": [{"RerunIfChanged": {"output": "release/build/parking_lot_core-e76957c6f392f35a/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}