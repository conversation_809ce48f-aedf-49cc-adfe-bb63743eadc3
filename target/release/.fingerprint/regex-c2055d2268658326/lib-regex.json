{"rustc": 12610991425282158916, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 16503403049695105087, "path": 8460690782461612133, "deps": [[555019317135488525, "regex_automata", false, 17915628885712369368], [2779309023524819297, "aho_corasick", false, 1920653320287443205], [9408802513701742484, "regex_syntax", false, 9481920541983376796], [15932120279885307830, "memchr", false, 6733121983210285479]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-c2055d2268658326/dep-lib-regex", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}