{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[]", "target": 17877812014956321412, "profile": 16503403049695105087, "path": 1036270380939805673, "deps": [[4684437522915235464, "libc", false, 7103501291586514795]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/signal-hook-registry-48ba3c37288dd779/dep-lib-signal_hook_registry", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}