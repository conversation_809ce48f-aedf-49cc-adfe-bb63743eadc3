{"rustc": 12610991425282158916, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 2781995403500503566, "path": 18078029560154492065, "deps": [[1457576002496728321, "clap_derive", false, 685602510380407634], [12790452388100355468, "clap_builder", false, 9659605632432466361]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-f35adff356ef76af/dep-lib-clap", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}