//! # Screenshot RS2
//!
//! A powerful screenshot library and command-line tool written in Rust.
//!
//! ## Features
//!
//! - Full-screen screenshot capture
//! - Region-based screenshot capture
//! - Multiple output formats (PNG, JPEG)
//! - Clipboard integration
//! - Cross-platform support
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use screenshot_rs2::capture::{take_full_screenshot, Region, take_region_screenshot};
//! use screenshot_rs2::output::handle_output;
//! use screenshot_rs2::cli::ImageFormat;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Take a full-screen screenshot
//!     let screenshot = take_full_screenshot()?;
//!     
//!     // Save to file
//!     handle_output(
//!         screenshot,
//!         Some("screenshot.png".into()),
//!         false,
//!         ImageFormat::Png,
//!     ).await?;
//!     
//!     Ok(())
//! }
//! ```

pub mod cli;
pub mod capture;
pub mod region_selector;
pub mod output;
pub mod error;

// Re-export commonly used types
pub use error::ScreenshotError;
pub use capture::{Region, take_full_screenshot, take_region_screenshot, get_screen_info};
pub use output::handle_output;
pub use cli::ImageFormat;
