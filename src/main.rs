pub mod cli;
pub mod capture;
pub mod region_selector;
pub mod output;
pub mod error;

use anyhow::Result;
use clap::Parser;
use cli::{Args, Command};
use log::{error, info};

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    
    let args = Args::parse();
    
    match run(args).await {
        Ok(_) => {
            info!("Screenshot operation completed successfully");
            Ok(())
        }
        Err(e) => {
            error!("Screenshot operation failed: {}", e);
            eprintln!("Error: {}", e);
            std::process::exit(1);
        }
    }
}

async fn run(args: Args) -> Result<()> {
    match args.command {
        Command::Full { output, clipboard, format } => {
            info!("Taking full-screen screenshot");
            let screenshot = capture::take_full_screenshot()?;
            output::handle_output(screenshot, output, clipboard, format).await?;
        }
        Command::Region { output, clipboard, format } => {
            info!("Starting interactive region selection");
            let region = region_selector::select_region().await?;
            let screenshot = capture::take_region_screenshot(region)?;
            output::handle_output(screenshot, output, clipboard, format).await?;
        }
    }
    Ok(())
}
