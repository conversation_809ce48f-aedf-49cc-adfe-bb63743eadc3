# Screenshot RS2

A powerful command-line screenshot tool written in Rust with interactive region selection capabilities.

## Features

- **Full-screen screenshots**: Capture the entire screen with a single command
- **Region selection**: Interactive text-based region selection for capturing specific areas
- **Multiple output options**: Save to files or copy directly to clipboard
- **Multiple image formats**: Support for PNG, JPG/JPEG formats
- **Cross-platform**: Works on macOS, Linux, and Windows
- **Fast and lightweight**: Built with Rust for optimal performance

## Installation

### From Source

1. Make sure you have Rust installed (https://rustup.rs/)
2. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/screenshot_rs2.git
   cd screenshot_rs2
   ```
3. Build the project:
   ```bash
   cargo build --release
   ```
4. The binary will be available at `target/release/screenshot`

### System Dependencies

On Linux, you may need to install additional dependencies:
```bash
# Ubuntu/Debian
sudo apt-get install libxcb1-dev libxrandr-dev libdbus-1-dev

# Fedora/RHEL
sudo dnf install libxcb-devel libXrandr-devel dbus-devel
```

## Usage

### Basic Commands

```bash
# Show help
screenshot --help

# Take a full-screen screenshot and save to file
screenshot full

# Take a full-screen screenshot and copy to clipboard
screenshot full --clipboard

# Take a full-screen screenshot with custom filename
screenshot full --output my_screenshot.png

# Take a region screenshot with interactive selection
screenshot region

# Take a region screenshot and copy to clipboard
screenshot region --clipboard

# Specify image format
screenshot full --format jpg
```

### Full-Screen Screenshots

```bash
# Save to default location (Desktop with timestamp)
screenshot full

# Save to specific file
screenshot full --output /path/to/screenshot.png

# Copy to clipboard only
screenshot full --clipboard

# Save to file AND copy to clipboard
screenshot full --output screenshot.png --clipboard

# Use JPEG format
screenshot full --format jpg
```

### Region Screenshots

```bash
# Interactive region selection
screenshot region

# The tool will prompt you for coordinates:
# X coordinate (left edge): 100
# Y coordinate (top edge): 100  
# Width: 800
# Height: 600
```

### Command-Line Options

#### Global Options
- `--help, -h`: Show help information
- `--version, -V`: Show version information

#### Subcommand Options
- `--output, -o <PATH>`: Specify output file path
- `--clipboard, -c`: Copy screenshot to clipboard
- `--format, -f <FORMAT>`: Image format (png, jpg, jpeg) [default: png]

## Examples

### Example 1: Quick Full-Screen Screenshot
```bash
screenshot full --clipboard
```
Takes a full-screen screenshot and copies it to the clipboard for immediate pasting.

### Example 2: Region Screenshot with Custom Name
```bash
screenshot region --output "ui_mockup.png"
```
Prompts for region coordinates and saves the selected area to "ui_mockup.png".

### Example 3: High-Quality JPEG Screenshot
```bash
screenshot full --format jpg --output "presentation_slide.jpg"
```
Takes a full-screen screenshot and saves it as a JPEG file.

## Configuration

The tool uses sensible defaults:
- Default format: PNG
- Default location: Desktop (with timestamp-based filename)
- Default behavior: Save to file (unless only --clipboard is specified)

## Error Handling

The tool provides clear error messages for common issues:
- No displays found
- Invalid region coordinates
- File permission errors
- Clipboard access issues

## Development

### Building from Source
```bash
cargo build
```

### Running Tests
```bash
cargo test
```

### Development Dependencies
The project uses several key dependencies:
- `screenshots`: Core screenshot capture functionality
- `clap`: Command-line argument parsing
- `image`: Image processing and format support
- `arboard`: Clipboard operations
- `anyhow`/`thiserror`: Error handling

## Roadmap

Future enhancements planned:
- [ ] GUI-based region selection overlay
- [ ] Multiple monitor support with selection
- [ ] Annotation tools (arrows, text, highlights)
- [ ] Delayed capture with countdown
- [ ] Batch screenshot operations
- [ ] Configuration file support
- [ ] Plugin system for custom outputs

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with the excellent `screenshots` crate
- Inspired by various screenshot tools across different platforms
- Thanks to the Rust community for the amazing ecosystem
