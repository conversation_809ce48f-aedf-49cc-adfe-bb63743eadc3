use screenshot_rs2::capture::{take_full_screenshot, take_region_screenshot, Region};
use screenshot_rs2::output::handle_output;
use screenshot_rs2::cli::ImageFormat;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Example 1: Take a full-screen screenshot
    println!("Taking full-screen screenshot...");
    let full_screenshot = take_full_screenshot()?;
    handle_output(
        full_screenshot,
        Some("example_full.png".into()),
        false,
        ImageFormat::Png,
    ).await?;
    println!("Full-screen screenshot saved to example_full.png");

    // Example 2: Take a region screenshot
    println!("Taking region screenshot...");
    let region = Region::new(100, 100, 400, 300);
    let region_screenshot = take_region_screenshot(region)?;
    handle_output(
        region_screenshot,
        Some("example_region.png".into()),
        false,
        ImageFormat::Png,
    ).await?;
    println!("Region screenshot saved to example_region.png");

    // Example 3: Copy to clipboard
    println!("Taking screenshot and copying to clipboard...");
    let clipboard_screenshot = take_full_screenshot()?;
    handle_output(
        clipboard_screenshot,
        None,
        true,
        ImageFormat::Png,
    ).await?;
    println!("Screenshot copied to clipboard");

    Ok(())
}
